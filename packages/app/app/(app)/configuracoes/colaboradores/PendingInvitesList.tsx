import React, { useState } from "react";
import { FlatList, Pressable, ActivityIndicator, Alert, Platform } from "react-native";
import {
  Box,
  Text,
  Badge,
  Center,
  Heading,
  Avatar,
  AvatarFallback,
  Button,
} from "@/components/ui";
import Modal from "@/components/Modal";
import { FeaturedIcon } from "@/components/FeaturedIcon";
import { useTeamInvitations } from "@/hooks/useTeamInvitations";
import { useDeleteInvitation } from "@/hooks/mutations/useDeleteInvitation";
import MailIcon from '@platform/assets/icons/mail.svg';
import TrashIcon from '@platform/assets/icons/delete-02.svg';

interface PendingInvitesListProps {
  teamId: string;
}

export function PendingInvitesList({ teamId }: PendingInvitesListProps) {
  const [invitationToDelete, setInvitationToDelete] = useState<any | null>(null);
  const deleteInvitationMutation = useDeleteInvitation();

  const {
    data: invitationsData,
    isLoading: isLoadingInvitations,
    error: errorInvitations,
  } = useTeamInvitations(teamId);

  const handleDeleteInvitation = (invitation: any) => {
    setInvitationToDelete(invitation);
  };

  const deleteInvitation = async (invitationId: string, teamId: string) => {
    try {
      await deleteInvitationMutation.mutateAsync({ invitationId, teamId });
      setInvitationToDelete(null);
      // Optionally, show a toast or a more subtle notification for success
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      console.error("Failed to delete invitation:", errorMessage);
      // Optionally, show a toast or a more subtle notification for error
      setInvitationToDelete(null);
    }
  };

  if (isLoadingInvitations) {
    return (
      <Center className="py-4">
        <ActivityIndicator size="large" />
        <Text className="mt-2">Carregando convites...</Text>
      </Center>
    );
  }

  if (errorInvitations) {
    return (
      <Text className="text-destructive py-4">
        Erro ao carregar convites: {errorInvitations.message}
      </Text>
    );
  }

  if (!invitationsData || invitationsData.length === 0) {
    return null;
  }

  return (
    <>
      <Heading className="mt-8 mb-1">Convites pendentes</Heading>
      <Text className="text-sm text-text-tertiary mb-4">
        Convites aguardando o cadastro do colaborador
      </Text>
      <FlatList
        data={invitationsData}
        keyExtractor={(item) => item.invitation_id}
        renderItem={({ item }) => (
          <Box className="group mb-2 flex-row items-center justify-between rounded-xl bg-background-dark p-3 shadow-sm">
            <Avatar alt={item.email} className="mr-4 bg-background">
              <AvatarFallback>
                <MailIcon className="h-4 w-4 text-text" />
              </AvatarFallback>
            </Avatar>
            <Box className="flex-1">
              <Box className="flex-row items-center">
                <Text className="font-medium text-foreground text-sm">{item.email}</Text>
                <Badge
                  className="ml-2 w-fit rounded-full"
                  variant={item.team_role === "owner" ? "primary-outline" : "outline"}
                  size="xs"
                >
                  <Text className="capitalize">{item.team_role === "owner" ? "Gestor" : "Colaborador"}</Text>
                </Badge>
              </Box>
              <Text className="mt-0.5 font-medium text-text-tertiary text-xs">
                Convidado em {new Date(item.created_at).toLocaleDateString()}
              </Text>
            </Box>
            <Pressable onPress={() => handleDeleteInvitation(item)} className="ml-2 h-8 w-8 rounded-full items-center justify-center bg-background opacity-0 group-hover:opacity-100">
              <TrashIcon className="h-4 w-4 text-destructive" />
            </Pressable>
          </Box>
        )}
      />

      {/* Delete Confirmation Modal */}
      <Modal isOpen={!!invitationToDelete} onClose={() => setInvitationToDelete(null)} size="sm">
        <Box className="p-6 items-center">
          <FeaturedIcon variant="modern-neue">
            <TrashIcon className="h-6 w-6 text-error" />
          </FeaturedIcon>
          <Heading size="md" className="mt-4 mb-2 text-foreground">
            Cancelar Convite
          </Heading>
          <Text className="mb-6 text-muted-foreground text-center">
            Tem certeza que deseja cancelar o convite para{" "}
            <Text className="font-bold text-foreground">{invitationToDelete?.email}</Text>?
            Esta ação não pode ser desfeita.
          </Text>

          <Box className="flex-row justify-end space-x-3 w-full">
            <Button variant="outline" onPress={() => setInvitationToDelete(null)} className="flex-1">
              <Text>Cancelar</Text>
            </Button>
            <Button
              variant="destructive"
              onPress={() => {
                if (invitationToDelete && teamId) {
                  deleteInvitation(invitationToDelete.invitation_id, teamId);
                }
              }}
              className="flex-1"
            >
              <Text>Sim, cancelar</Text>
            </Button>
          </Box>
        </Box>
      </Modal>
    </>
  );
}