import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { Box } from '@/components/ui/custom/box';
import { Text } from '@/components/ui/text';
import { cn } from '@/utils/cn';
import { LinearGradient } from 'expo-linear-gradient';

const featuredIconVariants = cva(
  'inline-flex items-center justify-center rounded-full',
  {
    variants: {
      variant: {
        light: 'bg-primary-100 text-primary-600',
        gradient: 'text-white',
        dark: 'bg-primary-900 text-white',
        outline: 'border border-border text-foreground',
        modern: 'border border-border bg-background text-foreground',
        'modern-neue': 'bg-primary-600 text-white',
      },
      size: {
        sm: 'h-10 w-10',
        md: 'h-12 w-12',
        lg: 'h-14 w-14',
      },
    },
    defaultVariants: {
      variant: 'light',
      size: 'md',
    },
  }
);

const featuredIconChildrenVariants = cva('', {
  variants: {
    size: {
      sm: 'h-5 w-5',
      md: 'h-6 w-6',
      lg: 'h-7 w-7',
    },
  },
  defaultVariants: {
    size: 'md',
  },
});

export interface FeaturedIconProps
  extends React.ComponentProps<typeof Box>,
    VariantProps<typeof featuredIconVariants> {
  children: React.ReactNode;
}

const FeaturedIcon = React.forwardRef<any, FeaturedIconProps>(
  ({ className, variant, size, children, ...props }, ref) => {
    const childrenWithProps = React.Children.map(children, (child) => {
      return React.cloneElement(child as React.ReactElement, {
        className: cn(
          featuredIconChildrenVariants({ size }),
          (child as React.ReactElement).props.className
        ),
      });
    });

    if (variant === 'gradient') {
      return (
        <LinearGradient
          colors={['#7F56D9', '#9E77ED']}
          className={cn(featuredIconVariants({ variant, size, className }))}
          ref={ref}
          {...props}
        >
          {childrenWithProps}
        </LinearGradient>
      );
    }

    return (
      <Box
        className={cn(featuredIconVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      >
        {childrenWithProps}
      </Box>
    );
  }
);

FeaturedIcon.displayName = 'FeaturedIcon';

export { FeaturedIcon, featuredIconVariants };