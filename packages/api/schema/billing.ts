import { boolean, integer, jsonb, pgEnum, pgSchema, pgTable, text, timestamp, uuid } from 'drizzle-orm/pg-core';

// Define schemas using pgSchema
export const basejumpSchema = pgSchema('basejump');
export const authSchema = pgSchema('auth');

// Define the subscription_status enum
export const subscriptionStatusEnum = basejumpSchema.enum('subscription_status', [
  'trialing',
  'active',
  'canceled',
  'incomplete',
  'incomplete_expired',
  'past_due',
  'unpaid',
  'paused', // Added 'paused' status based on previous conversations
]);

// Define the account_role enum
export const accountRoleEnum = basejumpSchema.enum('account_role', [
  'owner',
  'admin',
  'member',
]);

// Define the team_accounts table
export const teamAccounts = basejumpSchema.table('team_accounts', {
  id: uuid('id').primaryKey(),
  name: text('name'),
  slug: text('slug'),
  primaryOwnerUserId: uuid('primary_owner_user_id'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
  publicMetadata: jsonb('public_metadata'),
});

// Define the billing_customers table
export const billingCustomers = basejumpSchema.table('billing_customers', {
  id: text('id').primaryKey(),
  accountId: uuid('account_id').references(() => teamAccounts.id, { onDelete: 'cascade' }),
  email: text('email'),
  active: boolean('active'),
  provider: text('provider'),
});

// Define the billing_subscriptions table
export const billingSubscriptions = basejumpSchema.table('billing_subscriptions', {
  id: text('id').primaryKey(),
  teamAccountId: uuid('team_account_id').references(() => teamAccounts.id, { onDelete: 'cascade' }),
  billingCustomerId: text('billing_customer_id').references(() => billingCustomers.id, { onDelete: 'cascade' }),
  status: subscriptionStatusEnum('status'),
  metadata: jsonb('metadata'),
  priceId: text('price_id'),
  planName: text('plan_name'),
  quantity: integer('quantity'),
  cancelAtPeriodEnd: boolean('cancel_at_period_end'),
  created: timestamp('created').defaultNow(),
  currentPeriodStart: timestamp('current_period_start').defaultNow(),
  currentPeriodEnd: timestamp('current_period_end'),
  endedAt: timestamp('ended_at'),
  cancelAt: timestamp('cancel_at'),
  canceledAt: timestamp('canceled_at'),
  trialStart: timestamp('trial_start'),
  trialEnd: timestamp('trial_end'),
  provider: text('provider'),
});

// Define the auth.users table (simplified for our needs)
export const authUsers = authSchema.table('users', {
  id: uuid('id').primaryKey(),
  email: text('email'),
});

// Define the config table
export const config = basejumpSchema.table('config', {
  billingProvider: text('billing_provider'),
  enableTeamAccountBilling: boolean('enable_team_account_billing'),
});

// Define the team_users table for role information
export const teamUsers = basejumpSchema.table('team_users', {
  teamId: uuid('team_id').references(() => teamAccounts.id, { onDelete: 'cascade' }),
  userId: uuid('user_id'),
  teamRole: accountRoleEnum('team_role'),
});

// Define the team_invites table
export const teamInvites = basejumpSchema.table('team_invites', {
  id: uuid('id').primaryKey(),
  teamRole: accountRoleEnum('team_role').notNull(),
  teamId: uuid('team_id').notNull().references(() => teamAccounts.id, { onDelete: 'cascade' }),
  token: text('token').notNull(),
  invitedByUserId: uuid('invited_by_user_id'),
  teamName: text('team_name'),
  email: text('email').notNull(),
  updatedAt: timestamp('updated_at'),
  createdAt: timestamp('created_at'),
});
